import { Cascade, Entity, OneToOne, PrimaryKeyProp, Property, type Rel } from '@mikro-orm/core';
import { Player } from './Player.js';

@Entity({ tableName: 'player_attributes' })
export class PlayerAttributes {
  [PrimaryKeyProp]?: 'player';

  @OneToOne({
    entity: () => Player,
    fieldName: 'player_id',
    primary: true,
    cascade: [Cascade.REMOVE],
  })
  player!: Rel<Player>;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  reflexesCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  reflexesPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  positioningCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  positioningPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  shotStoppingCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  shotStoppingPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  tacklingCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  tacklingPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  markingCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  markingPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  headingCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  headingPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  finishingCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  finishingPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  paceCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  pacePotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  crossingCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  crossingPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  passingCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  passingPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  visionCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  visionPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  ballControlCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  ballControlPotential!: number;

  @Property({ type: 'float', precision: 2 })
  stamina!: number;
}
