import { Fixture } from '@/entities/Fixture.js';
import { Player } from '@/entities/Player.js';
import { SQS } from '@/services/sqs/sqs.js';
import { FixtureRepository } from '@/storage-interface/fixtures/index.js';
import { SimulateFixturesEvent } from '@/types/generated/simulate-fixtures-event.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { Ajv } from 'ajv';
import simulateFixturesEventSchema from '../../schemas/simulate-fixtures-event.json' with { type: 'json' };

const sqs = new SQS({ tracer: tracer });
const ajv = new Ajv();
const validateSimulateFixturesEvent = ajv.compile(simulateFixturesEventSchema);

// Multiply the players stamina by this and add this many energy points * hours since last match
export const STAMINA_SCALAR = 5.8;

export function updatePlayerEnergy(players: Player[]) {
  for (const player of players) {
    const hoursSinceLastMatch = (Date.now() - player.lastMatchPlayed) / (1000 * 60 * 60);
    const staminaScalar = player.attributes.stamina * STAMINA_SCALAR;
    player.energy = Math.min(100, player.energy + staminaScalar * hoursSinceLastMatch);
  }
}

/**
 * Fetches fixtures for a specific gameworld and league
 */
export async function getFixtures(
  fixtureRepository: FixtureRepository,
  gameworldId: string,
  leagueId: string
): Promise<Fixture[]> {
  return fixtureRepository.getFixturesByLeague(gameworldId, leagueId);
}

/**
 * Finds all unplayed fixtures with dates in the past
 */
export async function findDueFixtures(
  fixtureRepository: FixtureRepository,
  gameworldId?: string,
  leagueId?: string
): Promise<Fixture[]> {
  return fixtureRepository.getDueFixtures(gameworldId, leagueId);
}

/**
 * Processes next batch of fixtures for a specific gameworld and league by date
 * This is a debug method called to save us waiting for the scheduled lambda
 */
export async function debugProcessNextFixtures(
  fixtureRepository: FixtureRepository,
  gameworldId: string,
  leagueId: string
): Promise<Fixture[]> {
  const unplayedFixtures = await fixtureRepository.getAllUnplayedFixtures(gameworldId, leagueId);

  // If no unplayed fixtures, return empty array
  if (unplayedFixtures.length === 0) {
    logger.debug('No unplayed fixtures found', { gameworldId, leagueId });
    return [];
  }

  // get first date and filter to get all fixtures with the same date
  const firstDate = unplayedFixtures[0]!.date;
  const fixturesToSimulate = unplayedFixtures.filter((fixture) => fixture.date === firstDate);

  logger.debug(`Sending ${fixturesToSimulate.length} fixtures to SQS`, {
    fixtures: fixturesToSimulate.map((f) => f.fixtureId),
    gameworldId,
    leagueId,
  });

  return fixturesToSimulate;
}

/**
 * Sends fixtures to the SQS queue for simulation
 */
export async function sendFixturesToQueue(fixtures: Fixture[]): Promise<void> {
  if (!process.env.QUEUE_URL) {
    throw new Error('QUEUE_URL environment variable not set');
  }

  logger.debug(`Sending ${fixtures.length} fixtures to SQS`);

  await Promise.all(
    fixtures.map(async (fixture) => {
      const sqsPayload: SimulateFixturesEvent = {
        gameworldId: fixture.gameworldId,
        leagueId: fixture.league.id,
        fixtureId: fixture.fixtureId,
        homeTeamId: fixture.homeTeam.teamId,
        homeTeamName: fixture.homeTeam.teamName,
        awayTeamId: fixture.awayTeam.teamId,
        awayTeamName: fixture.awayTeam.teamName,
        date: fixture.date,
      };

      const valid = validateSimulateFixturesEvent(sqsPayload);
      if (!valid) {
        logger.error('Invalid fixture payload', {
          errors: validateSimulateFixturesEvent.errors,
          payload: sqsPayload,
        });
        throw new Error('Invalid fixture payload for SQS', {
          cause: validateSimulateFixturesEvent.errors,
        });
      }
      return sqs.send(process.env.QUEUE_URL!, JSON.stringify(sqsPayload));
    })
  );
}
