// Constants for the seeded random number generator
import { Player } from '@/entities/Player.js';
import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { BaseAttributeName } from '@/types/attribute-utils.js';
import { seededRandom } from '@/utils/seeded-random.js';

/**
 * Helper function to set attribute values on a PlayerAttributes instance
 * @param attributes The PlayerAttributes instance
 * @param attrName The base attribute name
 * @param current The current value
 * @param potential The potential value
 */
function setAttributeValues(
  attributes: PlayerAttributes,
  attrName: BaseAttributeName,
  current: number,
  potential: number
): void {
  switch (attrName) {
    case 'reflexes':
      attributes.reflexesCurrent = current;
      attributes.reflexesPotential = potential;
      break;
    case 'positioning':
      attributes.positioningCurrent = current;
      attributes.positioningPotential = potential;
      break;
    case 'shotStopping':
      attributes.shotStoppingCurrent = current;
      attributes.shotStoppingPotential = potential;
      break;
    case 'tackling':
      attributes.tacklingCurrent = current;
      attributes.tacklingPotential = potential;
      break;
    case 'marking':
      attributes.markingCurrent = current;
      attributes.markingPotential = potential;
      break;
    case 'heading':
      attributes.headingCurrent = current;
      attributes.headingPotential = potential;
      break;
    case 'finishing':
      attributes.finishingCurrent = current;
      attributes.finishingPotential = potential;
      break;
    case 'pace':
      attributes.paceCurrent = current;
      attributes.pacePotential = potential;
      break;
    case 'crossing':
      attributes.crossingCurrent = current;
      attributes.crossingPotential = potential;
      break;
    case 'passing':
      attributes.passingCurrent = current;
      attributes.passingPotential = potential;
      break;
    case 'vision':
      attributes.visionCurrent = current;
      attributes.visionPotential = potential;
      break;
    case 'ballControl':
      attributes.ballControlCurrent = current;
      attributes.ballControlPotential = potential;
      break;
  }
}

/**
 * Helper function to get attribute values from a PlayerAttributes instance
 * @param attributes The PlayerAttributes instance
 * @param attrName The base attribute name
 * @returns A tuple of [current, potential] values
 */
function getAttributeValues(
  attributes: PlayerAttributes,
  attrName: BaseAttributeName
): [number, number] {
  switch (attrName) {
    case 'reflexes':
      return [attributes.reflexesCurrent, attributes.reflexesPotential];
    case 'positioning':
      return [attributes.positioningCurrent, attributes.positioningPotential];
    case 'shotStopping':
      return [attributes.shotStoppingCurrent, attributes.shotStoppingPotential];
    case 'tackling':
      return [attributes.tacklingCurrent, attributes.tacklingPotential];
    case 'marking':
      return [attributes.markingCurrent, attributes.markingPotential];
    case 'heading':
      return [attributes.headingCurrent, attributes.headingPotential];
    case 'finishing':
      return [attributes.finishingCurrent, attributes.finishingPotential];
    case 'pace':
      return [attributes.paceCurrent, attributes.pacePotential];
    case 'crossing':
      return [attributes.crossingCurrent, attributes.crossingPotential];
    case 'passing':
      return [attributes.passingCurrent, attributes.passingPotential];
    case 'vision':
      return [attributes.visionCurrent, attributes.visionPotential];
    case 'ballControl':
      return [attributes.ballControlCurrent, attributes.ballControlPotential];
    default:
      return [0, 0]; // Fallback for TypeScript exhaustiveness checking
  }
}

// Constants for skill range adjustments
const SKILL_RANGE_OFFSET = 2; // Defines variation around the base skill level
export const ATTRIBUTE_MIN = 1; // Minimum possible attribute value
export const ATTRIBUTE_MAX = 40; // Maximum possible attribute value
const RANDOM_FACTOR_RANGE = 4; // Determines the spread of the random variation
const RANDOM_FACTOR_OFFSET = 2; // Centers the random variation around 0
const FOCUS_CATEGORY_WEIGHT = 1; // Increased weight for focus categories
const POTENTIAL_RANGE = 10; // Determines how much more than current ability potential can be
/**
 * Generates a PlayerAttributes entity instance with randomly generated attribute values
 * @param skillLevel The base skill level to use for generating attributes
 * @param minPotential The minimum potential value
 * @param maxPotential The maximum potential value
 * @param focusCategories Categories to emphasize when generating attributes
 * @returns A new PlayerAttributes instance
 */
export function generatePlayerAttributes(
  skillLevel: number,
  minPotential: number,
  maxPotential: number,
  focusCategories: BaseAttributeName[]
): PlayerAttributes {
  // Create a new PlayerAttributes instance
  const attributes = new PlayerAttributes();

  const DEFAULT_CATEGORY_WEIGHT = Math.pow(seededRandom(), 2) * 0.75 + 0.25; // Default weight for non-focus categories
  const baseValue = skillLevel - SKILL_RANGE_OFFSET;

  // Assign base weights to all categories
  const categoryWeights: Record<BaseAttributeName, number> = {
    reflexes: DEFAULT_CATEGORY_WEIGHT,
    positioning: DEFAULT_CATEGORY_WEIGHT,
    shotStopping: DEFAULT_CATEGORY_WEIGHT,
    tackling: DEFAULT_CATEGORY_WEIGHT,
    marking: DEFAULT_CATEGORY_WEIGHT,
    heading: DEFAULT_CATEGORY_WEIGHT,
    finishing: DEFAULT_CATEGORY_WEIGHT,
    pace: DEFAULT_CATEGORY_WEIGHT,
    crossing: DEFAULT_CATEGORY_WEIGHT,
    passing: DEFAULT_CATEGORY_WEIGHT,
    vision: DEFAULT_CATEGORY_WEIGHT,
    ballControl: DEFAULT_CATEGORY_WEIGHT,
  };

  // Increase weights for the specified focus categories
  focusCategories.forEach((category) => {
    categoryWeights[category] = FOCUS_CATEGORY_WEIGHT;
  });

  // Function to generate a single attribute's current and potential values
  function generateAttributeValues(name: BaseAttributeName): [number, number] {
    const weight = categoryWeights[name];
    const randomFactor = seededRandom() * RANDOM_FACTOR_RANGE - RANDOM_FACTOR_OFFSET; // Random value between -2 and 2
    const current = Math.min(
      ATTRIBUTE_MAX,
      Math.max(ATTRIBUTE_MIN, Math.round((baseValue + randomFactor) * weight))
    );
    const potential = generatePotential(current, minPotential, maxPotential);
    return [current, potential];
  }

  // Generate all attributes directly on the PlayerAttributes instance
  const baseAttributes: BaseAttributeName[] = [
    'reflexes',
    'positioning',
    'shotStopping',
    'tackling',
    'marking',
    'heading',
    'finishing',
    'pace',
    'crossing',
    'passing',
    'vision',
    'ballControl',
  ];

  // Apply the generated values directly to the PlayerAttributes instance
  baseAttributes.forEach((attr) => {
    const [current, potential] = generateAttributeValues(attr);
    setAttributeValues(attributes, attr, current, potential);
  });

  attributes.stamina = seededRandom(); // random value 0-1

  return attributes;
}

/**
 * Generate a potential value for an attribute
 * @param current
 * @param minPotential
 * @param maxPotential
 */
function generatePotential(current: number, minPotential: number, maxPotential: number): number {
  const lower = Math.max(current + 1, minPotential);
  const upper = Math.min(maxPotential, ATTRIBUTE_MAX);

  if (lower > upper) return lower; // fallback: can't go higher

  // Exponential bias: higher values are less likely
  const rand = seededRandom(); // 0..1
  const exp = 2; // increase for steeper drop-off
  const biased = 1 - Math.pow(1 - rand, exp); // bias towards lower values

  const potential = Math.round(lower + (upper - lower) * biased);
  return Math.max(lower, Math.min(potential, upper));
}

// Function to calculate a player's market value
export function calculatePlayerValue(player: Player): number {
  const CATEGORY_MULTIPLIER = 750000; // Multiplier for fully maxed-out category
  const AGE_FACTOR = 0.05; // Influence of age on value

  if (!player.attributes) {
    return 0; // Return 0 if player has no attributes
  }

  const attrs = player.attributes;

  const goalKeepingAbility =
    (attrs.reflexesCurrent + attrs.positioningCurrent + attrs.shotStoppingCurrent) / 3;
  const defendingAbility =
    (attrs.tacklingCurrent + attrs.markingCurrent + attrs.headingCurrent) / 3;
  const attackingAbility = (attrs.finishingCurrent + attrs.paceCurrent + attrs.crossingCurrent) / 3;
  const midfieldAbility =
    (attrs.passingCurrent + attrs.visionCurrent + attrs.ballControlCurrent) / 3;

  const goalKeepingValue = Math.pow(goalKeepingAbility / ATTRIBUTE_MAX, 2) * CATEGORY_MULTIPLIER;
  const defendingValue = Math.pow(defendingAbility / ATTRIBUTE_MAX, 2) * CATEGORY_MULTIPLIER;
  const attackingValue = Math.pow(attackingAbility / ATTRIBUTE_MAX, 2) * CATEGORY_MULTIPLIER;
  const midfieldValue = Math.pow(midfieldAbility / ATTRIBUTE_MAX, 2) * CATEGORY_MULTIPLIER;

  // Age factor: Younger players with high potential are more valuable
  const ageAdjustment = Math.min(1, 1 - (player.age - 30) * AGE_FACTOR);

  // Base value calculation
  return (goalKeepingValue + defendingValue + attackingValue + midfieldValue) * ageAdjustment;
}

export function formatPlayerAttributes(attributes: PlayerAttributes): string {
  const columnWidth = 20;
  const formatAttribute = (name: BaseAttributeName, attr: PlayerAttributes) => {
    const [current, potential] = getAttributeValues(attr, name);
    return `${name.padEnd(12)}${current.toString().padStart(2)}|${potential.toString().padStart(2)}`;
  };

  const goalkeeper = [
    'GOALKEEPING',
    formatAttribute('reflexes', attributes),
    formatAttribute('positioning', attributes),
    formatAttribute('shotStopping', attributes),
  ];

  const defender = [
    'DEFENDING',
    formatAttribute('tackling', attributes),
    formatAttribute('marking', attributes),
    formatAttribute('heading', attributes),
  ];

  const midfielder = [
    'MIDFIELD',
    formatAttribute('passing', attributes),
    formatAttribute('vision', attributes),
    formatAttribute('ballControl', attributes),
  ];

  const attacker = [
    'ATTACKING',
    formatAttribute('finishing', attributes),
    formatAttribute('pace', attributes),
    formatAttribute('crossing', attributes),
  ];

  // Pad columns to align
  const columns = [goalkeeper, defender, midfielder, attacker].map((col) =>
    col.map((line) => line.padEnd(columnWidth))
  );

  // Build output line by line
  let output = '';
  for (let i = 0; i < 4; i++) {
    output += columns.map((col) => col[i]).join(' ') + '\n';
  }

  return output;
}
