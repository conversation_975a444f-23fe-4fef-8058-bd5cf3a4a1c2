import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { TRAINING_MULTIPLIERS } from '@/functions/training/constants.js';
import { eventMiddify } from '@/middleware/event/index.ts';
import { EventHandler, EventWithRepositories } from '@/middleware/event/types.js';
import { SQS } from '@/services/sqs/sqs.js';
import { TrainingImprovementEvent } from '@/types/generated/training-improvement-event.js';
import { tracer } from '@/utils/tracer.js';
import { SendMessageBatchRequestEntry } from '@aws-sdk/client-sqs/dist-types/models/models_0.js';

const sqs = new SQS({ tracer });

const main: EventHandler<EventWithRepositories, void> = async (event) => {
  const { trainingRepository } = event.context.repositories;
  const filledSlots = await trainingRepository.getAllFilledSlots();

  if (filledSlots.length === 0) {
    return;
  }

  // Filter out slots that are already at potential
  const payload: SendMessageBatchRequestEntry[] = filledSlots
    .filter(
      (slot) =>
        slot.player!.attributes[`${slot.attribute}Current` as keyof PlayerAttributes]! !==
        slot.player!.attributes[`${slot.attribute}Potential` as keyof PlayerAttributes]!
    )
    .map((slot) => {
      const entry: SendMessageBatchRequestEntry = {
        Id: slot.id,
        MessageBody: JSON.stringify({
          playerId: slot.player!.playerId,
          playerName: `${slot.player!.firstName} ${slot.player!.surname}`,
          managerId: slot.team.manager?.managerId,
          attribute: slot.attribute,
          current: slot.player!.attributes[`${slot.attribute}Current` as keyof PlayerAttributes]!,
          potential:
            slot.player!.attributes[`${slot.attribute}Potential` as keyof PlayerAttributes]!,
          trainingMultiplier:
            TRAINING_MULTIPLIERS[
              Math.max(0, Math.min(TRAINING_MULTIPLIERS.length, slot.team.trainingLevel - 1))
            ],
        } as TrainingImprovementEvent),
      };
      return entry;
    });

  await sqs.sendBatch(process.env.TRAINING_QUEUE_URL!, payload);
};

export const handler = eventMiddify(main, { injectRepositories: true });
