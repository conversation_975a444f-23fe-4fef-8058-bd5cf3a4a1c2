import { Repositories } from '@/middleware/database/types.js';
import { vi } from 'vitest';

/**
 * Mock repositories for testing
 *
 * These mocks implement all methods from the repository interfaces
 * to ensure type safety and complete mocking for tests.
 */

export const mockEntityManager = {
  nativeUpdate: vi.fn(),
  nativeDelete: vi.fn(),
  find: vi.fn(),
};
/**
 * Mock implementation of TeamRepository
 */
export const mockTeamRepository = {
  batchInsertTeams: vi.fn(),
  batchInsertAvailableTeams: vi.fn(),
  getTeamsByGameworld: vi.fn(),
  getTeamsByLeague: vi.fn(),
  updateTeamLeague: vi.fn(),
  resetTeamStandings: vi.fn(),
  updateTeamLeagues: vi.fn(),
  getRandomAvailableTeam: vi.fn(),
  deleteAvailableTeam: vi.fn(),
  updateTeamStandings: vi.fn(),
  getTeam: vi.fn(),
  findByIds: vi.fn(),
  getTeamsWithoutManager: vi.fn(),
  updateTeamBalance: vi.fn(),
  updateTeamSelectionOrder: vi.fn(),
  getTeamAndNextMatch: vi.fn(),
  flush: vi.fn(),
  createFromPK: vi.fn().mockImplementation((id: string) => ({
    teamId: id,
  })),
  getEntityManager: vi.fn().mockReturnValue(mockEntityManager),
  incrementTrainingLevel: vi.fn(),
};

/**
 * Mock implementation of ManagerRepositoryInterface
 */
export const mockManagerRepository = {
  createManager: vi.fn(),
  getManagerById: vi.fn(),
  updateManager: vi.fn(),
  deleteManager: vi.fn(),
  getEntityManager: vi.fn().mockReturnValue(mockEntityManager),
  createFromPK: vi.fn(),
};

/**
 * Mock implementation of PlayerRepository
 */
export const mockPlayerRepository = {
  batchCreatePlayers: vi.fn(),
  getPlayer: vi.fn(),
  getPlayersByTeam: vi.fn(),
  getPlayersByLeague: vi.fn(),
  getPlayersWithoutTeam: vi.fn(),
  getPlayersByGameworld: vi.fn(),
  updatePlayer: vi.fn(),
  updatePlayerStats: vi.fn(),
  assignPlayerToTeam: vi.fn(),
  removePlayerFromTeam: vi.fn(),
  removePlayer: vi.fn(),
  addPlayerMatchHistory: vi.fn(),
  getPlayerMatchHistory: vi.fn(),
  batchCreateTransferListedPlayers: vi.fn(),
  getTransferListedPlayers: vi.fn(),
  isPlayerScoutedByTeam: vi.fn(),
  getPlayersScoutedByTeam: vi.fn(),
  getRandomPlayersFromLeague: vi.fn(),
  createFromPK: vi.fn().mockImplementation((id: string) => ({
    playerId: id,
  })),
  mikroOrmService: vi.fn(),
  getEntityManager: vi.fn().mockReturnValue(mockEntityManager),
  updatePlayerAttributesBatch: vi.fn(),
};

/**
 * Mock implementation of LeagueRepository
 */
export const mockLeagueRepository = {
  batchCreateLeagues: vi.fn(),
  getLeaguesByGameworld: vi.fn(),
  getLeague: vi.fn(),
  getLeagueHierarchy: vi.fn(),
  updateLeague: vi.fn(),
  createFromPK: vi.fn(),
};

/**
 * Mock implementation of FixtureRepository
 */
export const mockFixtureRepository = {
  batchInsertFixtures: vi.fn(),
  getFixture: vi.fn(),
  getFixturesByLeague: vi.fn(),
  getFixturesByTeam: vi.fn(),
  getDueFixtures: vi.fn(),
  getAllUnplayedFixtures: vi.fn(),
  updateFixtureResult: vi.fn(),
};

/**
 * Mock implementation of ScoutingRepository
 */
export const mockScoutingRepository = {
  scoutRandomPlayersFromLeague: vi.fn(),
  scoutPlayersFromTeam: vi.fn(),
  isPlayerScoutedByTeam: vi.fn(),
  getPlayersScoutedByTeam: vi.fn(),
  saveScoutedPlayers: vi.fn(),
};

/**
 * Mock implementation of ScoutingRequestRepository
 */
export const mockScoutingRequestRepository = {
  createScoutingRequest: vi.fn(),
  getPendingScoutingRequests: vi.fn(),
  markScoutingRequestAsProcessed: vi.fn(),
  getScoutingRequestsByTeam: vi.fn(),
};

/**
 * Mock implementation of TransferRepository
 */
export const mockTransferRepository = {
  getTransferListedPlayers: vi.fn(),
  submitOffer: vi.fn(),
  getTransferRequest: vi.fn(),
  getTransferRequestsToAI: vi.fn(),
  getTransferRequestsBySeller: vi.fn(),
  getTransferRequestsByBuyer: vi.fn(),
  getTransferRequestsByPlayer: vi.fn(),
  submitCounterOffer: vi.fn(),
  deleteTransferRequest: vi.fn(),
  submitBid: vi.fn(),
  getCompletedAuctions: vi.fn(),
  updateTransferListedPlayer: vi.fn(),
  deleteTransferListedPlayer: vi.fn(),
  getActiveAuctions: vi.fn(),
  addTransferListedPlayer: vi.fn(),
  getTransferListedPlayersWithTeamBids: vi.fn(),
  flush: vi.fn(),
};

/**
 * Mock implementation of GameworldRepository
 */
export const mockGameworldRepository = {
  getGameworld: vi.fn(),
  getAllGameworlds: vi.fn(),
  getCompletedSeasons: vi.fn(),
  createGameworld: vi.fn(),
  updateGameworld: vi.fn(),
  updateGameworldEndDate: vi.fn(),
};

/**
 * Mock implementation of InboxRepository
 */
export const mockInboxRepository = {
  createMessage: vi.fn(),
  getMessage: vi.fn(),
  getAllMessages: vi.fn(),
  updateMessage: vi.fn(),
  deleteMessage: vi.fn(),
  getMessagesByGameworldAndTeam: vi.fn(),
  flush: vi.fn(),
};

export const mockTrainingRepository = {
  assignPlayerToSlot: vi.fn(),
  clearSlot: vi.fn(),
  getSlotsByTeam: vi.fn(),
  createFromPK: vi.fn(),
  getEntityManager: vi.fn().mockReturnValue(mockEntityManager),
  createSlot: vi.fn(),
  getSlotById: vi.fn(),
  getAllFilledSlots: vi.fn(),
};

/**
 * Creates a complete mock repositories object that can be used in tests
 * @returns A repositories object with all repository mocks
 */
export const createMockRepositories = (): Repositories => ({
  leagueRepository: mockLeagueRepository,
  teamRepository: mockTeamRepository,
  fixtureRepository: mockFixtureRepository,
  playerRepository: mockPlayerRepository,
  managerRepository: mockManagerRepository,
  scoutingRepository: mockScoutingRepository,
  scoutingRequestRepository: mockScoutingRequestRepository,
  transferRepository: mockTransferRepository,
  gameworldRepository: mockGameworldRepository,
  inboxRepository: mockInboxRepository,
  trainingRepository: mockTrainingRepository,
});

/**
 * Resets all mock repositories
 * Useful for cleaning up between tests
 */
export const resetAllRepositoryMocks = (): void => {
  // Reset all mocks from each repository
  Object.values(mockLeagueRepository).forEach((mock) => mock.mockReset());
  Object.values(mockTeamRepository).forEach((mock) => mock.mockReset());
  Object.values(mockFixtureRepository).forEach((mock) => mock.mockReset());
  Object.values(mockPlayerRepository).forEach((mock) => mock.mockReset());
  Object.values(mockManagerRepository).forEach((mock) => mock.mockReset());
  Object.values(mockScoutingRepository).forEach((mock) => mock.mockReset());
  Object.values(mockScoutingRequestRepository).forEach((mock) => mock.mockReset());
  Object.values(mockTransferRepository).forEach((mock) => mock.mockReset());
  Object.values(mockGameworldRepository).forEach((mock) => mock.mockReset());
  Object.values(mockInboxRepository).forEach((mock) => mock.mockReset());
  Object.values(mockTrainingRepository).forEach((mock) => mock.mockReset());
};
