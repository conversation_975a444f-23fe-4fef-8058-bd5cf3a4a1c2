import { sqsBatchWindow } from '../config';
import { createLambdaFunction } from '../lambda';
import {
  addLambdaPermissionForSQS,
  addQueueReadPolicyToRole,
  createDLQ,
  createQueue,
} from '../queue';
import { createCloudWatchAlarmTopic, createMonitoredEventSourceMapping } from '../queueMonitoring';
import { addSesRoleToLambda } from '../ses';

export function createEmailResources() {
  // Create DLQ for email queue
  const emailDLQ = createDLQ('emailQueue');

  // Create main email queue with DLQ
  const emailQueue = createQueue('emailQueue', emailDLQ);

  // Create a role with SES permissions for the email processing Lambda
  const processEmailRole = addSesRoleToLambda('processEmail');

  // Add SQS read permissions to the role
  const processEmailRoleWithSQS = addQueueReadPolicyToRole(
    'processEmail',
    emailQueue,
    processEmailRole
  );

  // Create the Lambda function with the SES role and SQS permissions
  const [processEmailLambda, processEmailLogGroup] = createLambdaFunction(
    'processEmailHandler',
    '../dist/email/processEmail',
    'index.handler',
    {
      QUEUE_URL: emailQueue.url,
    },
    processEmailRoleWithSQS,
    require('fs')
      .readdirSync('../dist/templates')
      .filter((file: string) => file.endsWith('.mjml'))
      .map((file: string) => ({
        source: `../dist/templates/${file}`,
        destination: `templates/${file}`,
      })),
    {
      memorySize: 256,
      timeout: 60,
    }
  );

  // Add Lambda Permission for SQS to invoke it
  addLambdaPermissionForSQS('processEmail', processEmailLambda, emailQueue);

  // Create an SNS topic for CloudWatch alarms
  const errorAlarmTopic = createCloudWatchAlarmTopic('email-error');

  // Add SQS Queue Event Source Mapping to Lambda with monitoring
  createMonitoredEventSourceMapping(
    'processEmail',
    processEmailLambda,
    processEmailLogGroup,
    emailQueue,
    10, // batchSize
    sqsBatchWindow, // maximumBatchingWindowInSeconds
    undefined, // additionalConfig
    [errorAlarmTopic.arn] // alarmActions
  );

  return {
    emailQueue,
    emailQueueUrl: emailQueue.url,
    processEmailLambda,
    errorAlarmTopic,
  };
}
